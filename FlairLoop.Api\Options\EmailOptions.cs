﻿﻿using System.ComponentModel.DataAnnotations;

namespace FlairLoop.Api.Options;

public class EmailOptions
{
    public static string SectionName => "Email";

    [Required]
    public string SmtpServer { get; set; } = string.Empty;

    [Required]
    [Range(1, 65535)]
    public int SmtpPort { get; set; }

    [Required]
    public string SmtpUsername { get; set; } = string.Empty;

    [Required]
    public string SmtpPassword { get; set; } = string.Empty;

    [Required]
    public string FromEmail { get; set; } = string.Empty;

    [Required]
    public string FromName { get; set; } = string.Empty;

    public bool EnableSsl { get; set; } = true;
}
