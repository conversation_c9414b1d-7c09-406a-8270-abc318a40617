{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "ConnectionStrings": {"Database": "Host=localhost;Port=5432;Database=flairloop_db;Username=flairloop_user;Password=*************"}, "ClientOrigins": ["https://localhost:5220"], "Jwt": {"ValidIssuer": "https://localhost:5220", "ValidAudience": "https://localhost:5220", "AccessTokenLifeTime": 3600, "RefreshTokenLifeTime": 7200, "Secret": "XVCpHgOkn8ewoLJkz5cb4CND4lPy9DwuDSUZQVcZNw4="}, "Admin": {"Email": "<EMAIL>", "Username": "<EMAIL>", "Password": "emptyNebul@31"}, "Email": {"SmtpServer": "smtp-relay.brevo.com", "SmtpPort": 587, "SmtpUsername": "<EMAIL>", "SmtpPassword": "xsmtpsib-9f3d6d45c49f1d3570b2cd733693a25be75be9f6d0bc7abe2ca63e5e92dbaf4d-cq4dmZKY3W9VCMvz", "FromEmail": "<EMAIL>", "FromName": "<PERSON><PERSON><PERSON><PERSON>", "EnableSsl": true}}