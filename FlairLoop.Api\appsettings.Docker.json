{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "ConnectionStrings": {"Database": "from environment var"}, "ClientOrigins": ["https://localhost:80"], "Jwt": {"ValidIssuer": "https://localhost:80", "ValidAudience": "https://localhost:80", "AccessTokenLifeTime": 3600, "RefreshTokenLifeTime": 7200, "Secret": "from environment var"}, "Admin": {"Email": "from environment var", "Password": "from environment var"}, "Email": {"SmtpServer": "from environment var", "SmtpPort": 587, "SmtpUsername": "from environment var", "SmtpPassword": "xsmtpsib-9f3d6d45c49f1d3570b2cd733693a25be75be9f6d0bc7abe2ca63e5e92dbaf4d-cq4dmZKY3W9VCMvz", "FromEmail": "<EMAIL>", "FromName": "<PERSON><PERSON><PERSON><PERSON>", "EnableSsl": true}}