﻿﻿using FlairLoop.Api.Request;
using FlairLoop.Domain.Services;
using FlairLoop.Domain.Shared;
using Microsoft.AspNetCore.Http.HttpResults;

namespace FlairLoop.Api.Endpoints;

public static class EmailEndpoints
{
    public static void MapEmailEndpoints(this IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("api/email")
            .WithName("Email")
            .WithTags("Email")
            .WithOpenApi();

        group.MapPost("send", SendEmail)
            .WithName(nameof(SendEmail))
            .Accepts<SendEmailRequest>("application/json")
            .Produces<Ok>(StatusCodes.Status200OK)
            .Produces<BadRequest>(StatusCodes.Status400BadRequest)
            .Produces<ProblemHttpResult>(StatusCodes.Status500InternalServerError);
    }

    public static async Task<Results<Ok, BadRequest, ProblemHttpResult>> SendEmail(
        SendEmailRequest request,
        IEmailService emailService)
    {
        if (string.IsNullOrWhiteSpace(request.To))
        {
            return TypedResults.BadRequest();
        }

        var result = await emailService.SendEmailAsync(
            request.To,
            request.Subject,
            request.Body,
            request.IsHtml);

        if (result.IsFailure)
        {
            return TypedResults.Problem(result.Error?.Message);
        }

        return TypedResults.Ok();
    }
}
