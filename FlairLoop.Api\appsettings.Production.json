{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "ConnectionStrings": {"Database": "from environment var"}, "ClientOrigins": ["https://localhost:80"], "Jwt": {"ValidIssuer": "https://localhost:80", "ValidAudience": "https://localhost:80", "AccessTokenLifeTime": 43200, "RefreshTokenLifeTime": 7200, "Secret": "from environment var"}, "Admin": {"Email": "from environment var", "Password": "from environment var"}, "Email": {"SmtpServer": "from environment var", "SmtpPort": 587, "SmtpUsername": "from environment var", "SmtpPassword": "from environment var", "FromEmail": "<EMAIL>", "FromName": "<PERSON><PERSON><PERSON><PERSON>", "EnableSsl": true}}